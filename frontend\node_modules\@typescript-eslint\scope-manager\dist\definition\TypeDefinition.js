"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TypeDefinition = void 0;
const DefinitionBase_1 = require("./DefinitionBase");
const DefinitionType_1 = require("./DefinitionType");
class TypeDefinition extends DefinitionBase_1.DefinitionBase {
    isTypeDefinition = true;
    isVariableDefinition = false;
    constructor(name, node) {
        super(DefinitionType_1.DefinitionType.Type, name, node, null);
    }
}
exports.TypeDefinition = TypeDefinition;
