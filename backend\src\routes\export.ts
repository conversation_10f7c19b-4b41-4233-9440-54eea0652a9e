import express from 'express';
import { z } from 'zod';
import { prisma } from '../index';
import { Document, Packer, Paragraph, Table, TableCell, TableRow, TextRun, AlignmentType, WidthType } from 'docx';
import { startOfMonth, endOfMonth, format } from 'date-fns';
import { fr } from 'date-fns/locale';

const router = express.Router();

// Schéma de validation pour l'export
const exportSchema = z.object({
  month: z.number().min(1).max(12),
  year: z.number().min(2020).max(2030),
  includeStatistics: z.boolean().default(true),
  includeNotes: z.boolean().default(true),
  groupByDoctor: z.boolean().default(false),
  format: z.enum(['table', 'calendar']).default('table'),
  title: z.string().optional()
});

// POST /api/export/word - Exporter le planning en Word
router.post('/word', async (req, res) => {
  try {
    const validatedData = exportSchema.parse(req.body);
    const { month, year, includeStatistics, includeNotes, groupByDoctor, title } = validatedData;

    // Récupérer les données du mois
    const start = startOfMonth(new Date(year, month - 1));
    const end = endOfMonth(new Date(year, month - 1));

    const shifts = await prisma.shift.findMany({
      where: {
        date: {
          gte: start,
          lte: end
        }
      },
      include: {
        assistant: true,
        resident: true
      },
      orderBy: [
        { date: 'asc' }
      ]
    });

    const doctors = await prisma.doctor.findMany({
      where: {
        isActive: true
      },
      orderBy: [
        { lastName: 'asc' },
        { firstName: 'asc' }
      ]
    });

    // Créer le document Word
    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          // Titre
          new Paragraph({
            children: [
              new TextRun({
                text: title || `Planning des Gardes - ${format(start, 'MMMM yyyy', { locale: fr })}`,
                bold: true,
                size: 32
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { after: 400 }
          }),

          // Informations générales
          new Paragraph({
            children: [
              new TextRun({
                text: `Période: ${format(start, 'dd/MM/yyyy')} - ${format(end, 'dd/MM/yyyy')}`,
                size: 24
              })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: `Nombre total de gardes: ${shifts.length}`,
                size: 24
              })
            ],
            spacing: { after: 400 }
          }),

          // Table des gardes
          createShiftsTable(shifts, includeNotes),

          // Statistiques (si demandées)
          ...(includeStatistics ? createStatistics(shifts, doctors) : [])
        ]
      }]
    });

    // Générer le buffer
    const buffer = await Packer.toBuffer(doc);

    // Définir les headers pour le téléchargement
    const filename = `planning-gardes-${year}-${month.toString().padStart(2, '0')}.docx`;

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', buffer.length);

    res.send(buffer);

  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Données invalides',
        details: error.errors
      });
    }

    console.error('Erreur lors de l\'export Word:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de l\'export Word'
    });
  }
});

// Fonction pour créer la table des gardes
function createShiftsTable(shifts: any[], includeNotes: boolean) {
  const headers = ['Date', 'Horaire', 'Assistant', 'Résident'];

  const headerRow = new TableRow({
    children: headers.map(header =>
      new TableCell({
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: header,
                bold: true,
                color: 'FFFFFF'
              })
            ],
            alignment: AlignmentType.CENTER
          })
        ],
        shading: {
          fill: '1976d2'
        }
      })
    )
  });

  const dataRows = shifts.map(shift => {
    const cells = [
      // Date
      new TableCell({
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: format(new Date(shift.date), 'dd/MM/yyyy', { locale: fr })
              })
            ]
          })
        ]
      }),
      // Horaire
      new TableCell({
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: shift.timeSlot
              })
            ]
          })
        ]
      }),
      // Assistant
      new TableCell({
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: shift.assistant
                  ? `${shift.assistant.firstName} ${shift.assistant.lastName}`
                  : '-'
              })
            ]
          })
        ]
      }),
      // Résident
      new TableCell({
        children: [
          new Paragraph({
            children: [
              new TextRun({
                text: shift.resident
                  ? `${shift.resident.firstName} ${shift.resident.lastName}`
                  : '-'
              })
            ]
          })
        ]
      })
    ];

    return new TableRow({ children: cells });
  });

  return new Table({
    rows: [headerRow, ...dataRows],
    width: {
      size: 100,
      type: WidthType.PERCENTAGE
    }
  });
}

// Fonction pour créer les statistiques
function createStatistics(shifts: any[], doctors: any[]) {
  const statistics = [
    new Paragraph({
      children: [
        new TextRun({
          text: 'Statistiques',
          bold: true,
          size: 28
        })
      ],
      spacing: { before: 600, after: 300 }
    })
  ];

  // Statistiques par horaire
  const shiftsByTimeSlot = shifts.reduce((acc, shift) => {
    acc[shift.timeSlot] = (acc[shift.timeSlot] || 0) + 1;
    return acc;
  }, {});

  statistics.push(
    new Paragraph({
      children: [
        new TextRun({
          text: 'Répartition par horaire:',
          bold: true,
          size: 24
        })
      ],
      spacing: { after: 200 }
    })
  );

  Object.entries(shiftsByTimeSlot).forEach(([timeSlot, count]) => {
    statistics.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `• ${timeSlot}: ${count} garde(s)`,
            size: 22
          })
        ],
        spacing: { after: 100 }
      })
    );
  });

  // Statistiques par assistant
  const shiftsByAssistant = shifts.reduce((acc, shift) => {
    if (shift.assistant) {
      const doctorName = `${shift.assistant.firstName} ${shift.assistant.lastName}`;
      acc[doctorName] = (acc[doctorName] || 0) + 1;
    }
    return acc;
  }, {});

  statistics.push(
    new Paragraph({
      children: [
        new TextRun({
          text: 'Répartition par assistant:',
          bold: true,
          size: 24
        })
      ],
      spacing: { before: 300, after: 200 }
    })
  );

  Object.entries(shiftsByAssistant).forEach(([doctor, count]) => {
    statistics.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `• ${doctor}: ${count} garde(s)`,
            size: 22
          })
        ],
        spacing: { after: 100 }
      })
    );
  });

  // Statistiques par résident
  const shiftsByResident = shifts.reduce((acc, shift) => {
    if (shift.resident) {
      const doctorName = `${shift.resident.firstName} ${shift.resident.lastName}`;
      acc[doctorName] = (acc[doctorName] || 0) + 1;
    }
    return acc;
  }, {});

  statistics.push(
    new Paragraph({
      children: [
        new TextRun({
          text: 'Répartition par résident:',
          bold: true,
          size: 24
        })
      ],
      spacing: { before: 300, after: 200 }
    })
  );

  Object.entries(shiftsByResident).forEach(([doctor, count]) => {
    statistics.push(
      new Paragraph({
        children: [
          new TextRun({
            text: `• ${doctor}: ${count} garde(s)`,
            size: 22
          })
        ],
        spacing: { after: 100 }
      })
    );
  });

  return statistics;
}



export default router;
