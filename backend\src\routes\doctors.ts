import express from 'express';
import { z } from 'zod';
import { prisma } from '../index';

// Enum pour les types de médecins
enum DoctorType {
  RESIDENT = 'RESIDENT',
  ASSISTANT = 'ASSISTANT'
}

const router = express.Router();

// Schémas de validation
const createDoctorSchema = z.object({
  firstName: z.string().min(1, 'Le prénom est requis'),
  lastName: z.string().min(1, 'Le nom est requis'),
  type: z.nativeEnum(DoctorType)
});

const updateDoctorSchema = createDoctorSchema.partial();

// GET /api/doctors - Récupérer tous les médecins
router.get('/', async (req, res) => {
  try {
    const { type, isActive, search } = req.query;

    const where: any = {};

    if (type) where.type = type;
    if (isActive !== undefined) where.isActive = isActive === 'true';
    if (search) {
      where.OR = [
        { firstName: { contains: search as string, mode: 'insensitive' } },
        { lastName: { contains: search as string, mode: 'insensitive' } }
      ];
    }

    const doctors = await prisma.doctor.findMany({
      where,
      orderBy: [
        { lastName: 'asc' },
        { firstName: 'asc' }
      ]
    });

    res.json({
      success: true,
      data: doctors
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des médecins:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la récupération des médecins'
    });
  }
});

// GET /api/doctors/:id - Récupérer un médecin par ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const doctor = await prisma.doctor.findUnique({
      where: { id }
    });

    if (!doctor) {
      return res.status(404).json({
        success: false,
        error: 'Médecin non trouvé'
      });
    }

    res.json({
      success: true,
      data: doctor
    });
  } catch (error) {
    console.error('Erreur lors de la récupération du médecin:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la récupération du médecin'
    });
  }
});

// POST /api/doctors - Créer un nouveau médecin
router.post('/', async (req, res) => {
  try {
    const validatedData = createDoctorSchema.parse(req.body);

    const doctor = await prisma.doctor.create({
      data: validatedData
    });

    res.status(201).json({
      success: true,
      data: doctor,
      message: 'Médecin créé avec succès'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Données invalides',
        details: error.errors
      });
    }

    console.error('Erreur lors de la création du médecin:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la création du médecin'
    });
  }
});

// PUT /api/doctors/:id - Mettre à jour un médecin
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const validatedData = updateDoctorSchema.parse(req.body);

    // Vérifier si le médecin existe
    const existingDoctor = await prisma.doctor.findUnique({
      where: { id }
    });

    if (!existingDoctor) {
      return res.status(404).json({
        success: false,
        error: 'Médecin non trouvé'
      });
    }

    const doctor = await prisma.doctor.update({
      where: { id },
      data: validatedData
    });

    res.json({
      success: true,
      data: doctor,
      message: 'Médecin mis à jour avec succès'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Données invalides',
        details: error.errors
      });
    }

    console.error('Erreur lors de la mise à jour du médecin:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la mise à jour du médecin'
    });
  }
});

// DELETE /api/doctors/:id - Supprimer un médecin
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Vérifier si le médecin existe
    const existingDoctor = await prisma.doctor.findUnique({
      where: { id }
    });

    if (!existingDoctor) {
      return res.status(404).json({
        success: false,
        error: 'Médecin non trouvé'
      });
    }

    await prisma.doctor.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Médecin supprimé avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression du médecin:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la suppression du médecin'
    });
  }
});

export default router;
