import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typo<PERSON>,
  Button,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
} from '@mui/material';
import {
  People as PeopleIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  TrendingUp as TrendingIcon,
  CalendarToday as CalendarIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';

// Types locaux pour le dashboard
interface DashboardStats {
  totalDoctors: number;
  activeDoctors: number;
  totalShifts: number;
  confirmedShifts: number;
  pendingShifts: number;
  thisMonthShifts: number;
}

interface RecentShift {
  id: string;
  doctorName: string;
  date: string;
  type: string;
  isConfirmed: boolean;
}

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const [stats, setStats] = useState<DashboardStats>({
    totalDoctors: 0,
    activeDoctors: 0,
    totalShifts: 0,
    confirmedShifts: 0,
    pendingShifts: 0,
    thisMonthShifts: 0,
  });
  const [recentShifts, setRecentShifts] = useState<RecentShift[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // TODO: Remplacer par de vrais appels API
      // Simulation de données pour le moment
      setTimeout(() => {
        setStats({
          totalDoctors: 12,
          activeDoctors: 10,
          totalShifts: 156,
          confirmedShifts: 142,
          pendingShifts: 14,
          thisMonthShifts: 45,
        });

        setRecentShifts([
          {
            id: '1',
            doctorName: 'Dr. Marie Dubois',
            date: dayjs().format('DD/MM/YYYY'),
            type: 'Jour',
            isConfirmed: true,
          },
          {
            id: '2',
            doctorName: 'Dr. Pierre Martin',
            date: dayjs().add(1, 'day').format('DD/MM/YYYY'),
            type: 'Nuit',
            isConfirmed: false,
          },
          {
            id: '3',
            doctorName: 'Dr. Sophie Bernard',
            date: dayjs().add(2, 'day').format('DD/MM/YYYY'),
            type: 'Week-end',
            isConfirmed: true,
          },
        ]);

        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      setLoading(false);
    }
  };

  const getShiftTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'jour':
        return 'primary';
      case 'nuit':
        return 'secondary';
      case 'week-end':
        return 'warning';
      case 'jour férié':
        return 'error';
      default:
        return 'default';
    }
  };

  const StatCard: React.FC<{
    title: string;
    value: number;
    icon: React.ReactNode;
    color: string;
    subtitle?: string;
  }> = ({ title, value, icon, color, subtitle }) => (
    <Card elevation={2}>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="textSecondary">
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box color={color}>{icon}</Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Chargement...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Tableau de bord
      </Typography>
      <Typography variant="body1" color="textSecondary" paragraph>
        Vue d'ensemble de la gestion des gardes hospitalières
      </Typography>

      {/* Statistiques principales */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Médecins actifs"
            value={stats.activeDoctors}
            icon={<PeopleIcon fontSize="large" />}
            color="primary.main"
            subtitle={`sur ${stats.totalDoctors} total`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Gardes ce mois"
            value={stats.thisMonthShifts}
            icon={<CalendarIcon fontSize="large" />}
            color="secondary.main"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Gardes confirmées"
            value={stats.confirmedShifts}
            icon={<CheckIcon fontSize="large" />}
            color="success.main"
            subtitle={`sur ${stats.totalShifts} total`}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="En attente"
            value={stats.pendingShifts}
            icon={<WarningIcon fontSize="large" />}
            color="warning.main"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Gardes récentes */}
        <Grid item xs={12} md={8}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">Gardes récentes</Typography>
                <Button
                  variant="outlined"
                  size="small"
                  onClick={() => navigate('/shifts')}
                >
                  Voir tout
                </Button>
              </Box>
              <List>
                {recentShifts.map((shift, index) => (
                  <React.Fragment key={shift.id}>
                    <ListItem>
                      <ListItemIcon>
                        <ScheduleIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={shift.doctorName}
                        secondary={`${shift.date} - Garde de ${shift.type}`}
                      />
                      <Chip
                        label={shift.isConfirmed ? 'Confirmée' : 'En attente'}
                        color={shift.isConfirmed ? 'success' : 'warning'}
                        size="small"
                      />
                    </ListItem>
                    {index < recentShifts.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Actions rapides */}
        <Grid item xs={12} md={4}>
          <Card elevation={2}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Actions rapides
              </Typography>
              <Box display="flex" flexDirection="column" gap={2}>
                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<PeopleIcon />}
                  onClick={() => navigate('/doctors')}
                >
                  Gérer les médecins
                </Button>
                <Button
                  variant="contained"
                  fullWidth
                  startIcon={<ScheduleIcon />}
                  onClick={() => navigate('/shifts')}
                >
                  Planifier une garde
                </Button>
                <Button
                  variant="outlined"
                  fullWidth
                  startIcon={<CalendarIcon />}
                  onClick={() => navigate('/calendar')}
                >
                  Voir le calendrier
                </Button>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  💡 Astuce: Utilisez le calendrier pour une vue d'ensemble mensuelle des gardes.
                </Typography>
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
