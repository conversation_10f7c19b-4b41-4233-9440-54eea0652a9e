import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  <PERSON>,
  CardContent,
  Ty<PERSON>graphy,
  <PERSON>ton,
  Chip,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import {
  Add as AddIcon,
  Remove as RemoveIcon,
  FileDownload as ExportIcon,
  CalendarToday as CalendarIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers';
import dayjs, { Dayjs } from 'dayjs';

// Types pour l'application simplifiée
interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  type: 'RESIDENT' | 'ASSISTANT';
}

interface DoctorSelection {
  doctorId: string;
  doctor: Doctor;
  count: number;
}

interface Shift {
  id: string;
  date: string;
  timeSlot: string;
  assistantId?: string;
  residentId?: string;
  assistant?: Doctor;
  resident?: Doctor;
}

interface ExcludedDate {
  id: string;
  date: string;
  description: string;
}

const Dashboard: React.FC = () => {
  const [selectedMonth, setSelectedMonth] = useState(dayjs().month());
  const [selectedYear, setSelectedYear] = useState(dayjs().year());
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [selectedDoctors, setSelectedDoctors] = useState<DoctorSelection[]>([]);
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [excludedDates, setExcludedDates] = useState<ExcludedDate[]>([]);
  const [loading, setLoading] = useState(true);
  const [excludedDateDialog, setExcludedDateDialog] = useState(false);
  const [newExcludedDate, setNewExcludedDate] = useState<Dayjs | null>(dayjs());
  const [newExcludedDescription, setNewExcludedDescription] = useState('');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);

      // TODO: Remplacer par de vrais appels API
      // Simulation de données
      setTimeout(() => {
        const mockDoctors: Doctor[] = [
          { id: '1', firstName: 'Marie', lastName: 'Dubois', type: 'RESIDENT' },
          { id: '2', firstName: 'Pierre', lastName: 'Martin', type: 'ASSISTANT' },
          { id: '3', firstName: 'Sophie', lastName: 'Bernard', type: 'RESIDENT' },
          { id: '4', firstName: 'Thomas', lastName: 'Petit', type: 'ASSISTANT' },
          { id: '5', firstName: 'Julie', lastName: 'Moreau', type: 'RESIDENT' },
          { id: '6', firstName: 'Antoine', lastName: 'Leroy', type: 'ASSISTANT' },
        ];

        setDoctors(mockDoctors);

        // Initialiser la sélection avec tous les médecins à 0
        setSelectedDoctors(mockDoctors.map(doctor => ({
          doctorId: doctor.id,
          doctor,
          count: 0
        })));

        setExcludedDates([
          { id: '1', date: '2024-01-01', description: 'Nouvel An' },
          { id: '2', date: '2024-05-01', description: 'Fête du travail' },
          { id: '3', date: '2024-07-14', description: 'Fête nationale' },
          { id: '4', date: '2024-12-25', description: 'Noël' },
        ]);

        // Générer 34 créneaux vides
        const mockShifts: Shift[] = [];
        for (let i = 1; i <= 34; i++) {
          mockShifts.push({
            id: i.toString(),
            date: dayjs().add(i, 'day').format('YYYY-MM-DD'),
            timeSlot: i % 2 === 0 ? '20:00-08:00' : '08:00-20:00',
          });
        }
        setShifts(mockShifts);

        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      setLoading(false);
    }
  };

  const handleDoctorCountChange = (doctorId: string, increment: boolean) => {
    setSelectedDoctors(prev =>
      prev.map(selection =>
        selection.doctorId === doctorId
          ? {
              ...selection,
              count: Math.max(0, selection.count + (increment ? 1 : -1))
            }
          : selection
      )
    );
  };

  const handleAddExcludedDate = () => {
    if (newExcludedDate && newExcludedDescription.trim()) {
      const newDate: ExcludedDate = {
        id: Date.now().toString(),
        date: newExcludedDate.format('YYYY-MM-DD'),
        description: newExcludedDescription.trim()
      };
      setExcludedDates(prev => [...prev, newDate]);
      setNewExcludedDate(dayjs());
      setNewExcludedDescription('');
      setExcludedDateDialog(false);
    }
  };

  const handleRemoveExcludedDate = (id: string) => {
    setExcludedDates(prev => prev.filter(date => date.id !== id));
  };

  const generateShifts = () => {
    // TODO: Implémenter la génération automatique des gardes
    // basée sur les médecins sélectionnés et leurs compteurs
    console.log('Génération des gardes avec:', selectedDoctors);
  };

  const handleExport = async () => {
    try {
      const { exportService, downloadFile } = await import('../services/api');

      const blob = await exportService.exportToWord({
        month: selectedMonth + 1, // API attend 1-12, state utilise 0-11
        year: selectedYear,
        includeStatistics: true,
        title: `Planning des Gardes - ${months[selectedMonth]} ${selectedYear}`
      });

      const filename = `planning-gardes-${selectedYear}-${(selectedMonth + 1).toString().padStart(2, '0')}.docx`;
      downloadFile(blob, filename);

      // Afficher une notification de succès
      console.log('Export réussi !');
    } catch (error) {
      console.error('Erreur lors de l\'export:', error);
      // TODO: Afficher une notification d'erreur à l'utilisateur
    }
  };

  const months = [
    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Chargement...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* En-tête moderne */}
      <Box sx={{ mb: 4 }}>
        <Box
          sx={{
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            justifyContent: 'space-between',
            alignItems: { xs: 'flex-start', sm: 'center' },
            gap: 2,
            mb: 2
          }}
        >
          <Box>
            <Typography
              variant="h4"
              sx={{
                fontWeight: 700,
                background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 1
              }}
            >
              Planning des Gardes
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Gestion simplifiée des gardes hospitalières
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            <FormControl size="small" sx={{ minWidth: 140 }}>
              <InputLabel>Mois</InputLabel>
              <Select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(e.target.value as number)}
                label="Mois"
              >
                {months.map((month, index) => (
                  <MenuItem key={index} value={index}>
                    {month}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <FormControl size="small" sx={{ minWidth: 100 }}>
              <InputLabel>Année</InputLabel>
              <Select
                value={selectedYear}
                onChange={(e) => setSelectedYear(e.target.value as number)}
                label="Année"
              >
                {Array.from({ length: 5 }, (_, i) => dayjs().year() - 2 + i).map((year) => (
                  <MenuItem key={year} value={year}>
                    {year}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>

        {/* Indicateur de période sélectionnée */}
        <Box
          sx={{
            p: 2,
            bgcolor: 'primary.50',
            borderRadius: 2,
            border: '1px solid',
            borderColor: 'primary.200'
          }}
        >
          <Typography variant="body2" color="primary.main" sx={{ fontWeight: 500 }}>
            📅 Période sélectionnée: {months[selectedMonth]} {selectedYear}
          </Typography>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Sélection des médecins */}
        <Grid item xs={12} lg={6}>
          <Card
            elevation={0}
            sx={{
              height: 'fit-content',
              background: 'linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%)',
              border: '1px solid #e2e8f0'
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <Box
                  sx={{
                    p: 1.5,
                    borderRadius: 2,
                    bgcolor: 'primary.main',
                    color: 'white'
                  }}
                >
                  <PersonIcon />
                </Box>
                <Box>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Sélection des médecins
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Ajustez le nombre d'apparitions dans le planning
                  </Typography>
                </Box>
              </Box>

              {/* Assistants */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="subtitle1"
                  sx={{
                    mb: 2,
                    fontWeight: 600,
                    color: 'primary.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  👨‍⚕️ Assistants
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {selectedDoctors
                    .filter(selection => selection.doctor.type === 'ASSISTANT')
                    .map((selection) => (
                      <Box
                        key={selection.doctorId}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          p: 2,
                          bgcolor: 'white',
                          borderRadius: 2,
                          border: '1px solid #e2e8f0',
                          '&:hover': {
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                          }
                        }}
                      >
                        <Typography sx={{ fontWeight: 500 }}>
                          {selection.doctor.firstName} {selection.doctor.lastName}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <IconButton
                            size="small"
                            onClick={() => handleDoctorCountChange(selection.doctorId, false)}
                            disabled={selection.count === 0}
                            sx={{
                              bgcolor: selection.count === 0 ? 'grey.100' : 'error.50',
                              '&:hover': { bgcolor: 'error.100' }
                            }}
                          >
                            <RemoveIcon fontSize="small" />
                          </IconButton>
                          <Chip
                            label={selection.count}
                            color="primary"
                            size="small"
                            sx={{
                              minWidth: 45,
                              fontWeight: 600,
                              fontSize: '0.875rem'
                            }}
                          />
                          <IconButton
                            size="small"
                            onClick={() => handleDoctorCountChange(selection.doctorId, true)}
                            sx={{
                              bgcolor: 'success.50',
                              '&:hover': { bgcolor: 'success.100' }
                            }}
                          >
                            <AddIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </Box>
                    ))}
                </Box>
              </Box>

              {/* Résidents */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="subtitle1"
                  sx={{
                    mb: 2,
                    fontWeight: 600,
                    color: 'secondary.main',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 1
                  }}
                >
                  🩺 Résidents
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {selectedDoctors
                    .filter(selection => selection.doctor.type === 'RESIDENT')
                    .map((selection) => (
                      <Box
                        key={selection.doctorId}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'space-between',
                          p: 2,
                          bgcolor: 'white',
                          borderRadius: 2,
                          border: '1px solid #e2e8f0',
                          '&:hover': {
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                          }
                        }}
                      >
                        <Typography sx={{ fontWeight: 500 }}>
                          {selection.doctor.firstName} {selection.doctor.lastName}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <IconButton
                            size="small"
                            onClick={() => handleDoctorCountChange(selection.doctorId, false)}
                            disabled={selection.count === 0}
                            sx={{
                              bgcolor: selection.count === 0 ? 'grey.100' : 'error.50',
                              '&:hover': { bgcolor: 'error.100' }
                            }}
                          >
                            <RemoveIcon fontSize="small" />
                          </IconButton>
                          <Chip
                            label={selection.count}
                            color="secondary"
                            size="small"
                            sx={{
                              minWidth: 45,
                              fontWeight: 600,
                              fontSize: '0.875rem'
                            }}
                          />
                          <IconButton
                            size="small"
                            onClick={() => handleDoctorCountChange(selection.doctorId, true)}
                            sx={{
                              bgcolor: 'success.50',
                              '&:hover': { bgcolor: 'success.100' }
                            }}
                          >
                            <AddIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </Box>
                    ))}
                </Box>
              </Box>

              <Button
                variant="contained"
                fullWidth
                size="large"
                onClick={generateShifts}
                disabled={selectedDoctors.every(s => s.count === 0)}
                sx={{
                  py: 1.5,
                  fontWeight: 600,
                  fontSize: '1rem',
                  background: selectedDoctors.every(s => s.count === 0)
                    ? undefined
                    : 'linear-gradient(45deg, #1976d2, #42a5f5)',
                  '&:hover': {
                    background: 'linear-gradient(45deg, #1565c0, #1976d2)',
                  }
                }}
              >
                🚀 Générer le planning
              </Button>
            </CardContent>
          </Card>
        </Grid>

        {/* Gestion des dates exclues */}
        <Grid item xs={12} md={6}>
          <Card elevation={2}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Dates exclues (Jours fériés)
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<AddIcon />}
                  onClick={() => setExcludedDateDialog(true)}
                >
                  Ajouter
                </Button>
              </Box>

              <Typography variant="body2" color="textSecondary" paragraph>
                Dates à ne pas inclure dans le planning
              </Typography>

              {excludedDates.length === 0 ? (
                <Alert severity="info">
                  Aucune date exclue. Cliquez sur "Ajouter" pour en créer.
                </Alert>
              ) : (
                excludedDates.map((excludedDate) => (
                  <Box key={excludedDate.id} display="flex" alignItems="center" justifyContent="space-between" py={1}>
                    <Box>
                      <Typography variant="body1">
                        {dayjs(excludedDate.date).format('DD/MM/YYYY')}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {excludedDate.description}
                      </Typography>
                    </Box>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleRemoveExcludedDate(excludedDate.id)}
                    >
                      <RemoveIcon />
                    </IconButton>
                  </Box>
                ))
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Liste des 34 créneaux */}
      <Card elevation={2} sx={{ mt: 3 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
            <Typography variant="h6">
              Planning des gardes (34 créneaux)
            </Typography>
            <Button
              variant="contained"
              startIcon={<ExportIcon />}
              onClick={handleExport}
            >
              Exporter en Word
            </Button>
          </Box>

          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Date</TableCell>
                  <TableCell>Horaire</TableCell>
                  <TableCell>Assistant</TableCell>
                  <TableCell>Résident</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {shifts.map((shift) => (
                  <TableRow key={shift.id}>
                    <TableCell>
                      {dayjs(shift.date).format('DD/MM/YYYY')}
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={shift.timeSlot}
                        color={shift.timeSlot.includes('20:00') ? 'secondary' : 'primary'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {shift.assistant ?
                        `${shift.assistant.firstName} ${shift.assistant.lastName}` :
                        '-'
                      }
                    </TableCell>
                    <TableCell>
                      {shift.resident ?
                        `${shift.resident.firstName} ${shift.resident.lastName}` :
                        '-'
                      }
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Dialog pour ajouter une date exclue */}
      <Dialog open={excludedDateDialog} onClose={() => setExcludedDateDialog(false)}>
        <DialogTitle>Ajouter une date exclue</DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} pt={1}>
            <DatePicker
              label="Date"
              value={newExcludedDate}
              onChange={(newValue) => setNewExcludedDate(newValue)}
              slotProps={{ textField: { fullWidth: true } }}
            />
            <TextField
              label="Description"
              value={newExcludedDescription}
              onChange={(e) => setNewExcludedDescription(e.target.value)}
              fullWidth
              placeholder="Ex: Noël, Jour de l'An..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExcludedDateDialog(false)}>Annuler</Button>
          <Button onClick={handleAddExcludedDate} variant="contained">
            Ajouter
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Dashboard;
