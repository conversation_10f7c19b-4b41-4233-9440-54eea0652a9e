import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON>rid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Fab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Schedule as ScheduleIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { DatePicker, TimePicker } from '@mui/x-date-pickers';
import dayjs, { Dayjs } from 'dayjs';

// Types pour les gardes
interface Shift {
  id: string;
  doctorId: string;
  doctorName: string;
  date: string;
  startTime: string;
  endTime: string;
  type: 'DAY' | 'NIGHT' | 'WEEKEND' | 'HOLIDAY';
  location?: string;
  notes?: string;
  isConfirmed: boolean;
}

interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
}

const ShiftsPage: React.FC = () => {
  const [shifts, setShifts] = useState<Shift[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingShift, setEditingShift] = useState<Shift | null>(null);
  const [formData, setFormData] = useState({
    doctorId: '',
    date: dayjs() as Dayjs,
    startTime: dayjs().hour(8).minute(0) as Dayjs,
    endTime: dayjs().hour(20).minute(0) as Dayjs,
    type: 'DAY' as 'DAY' | 'NIGHT' | 'WEEKEND' | 'HOLIDAY',
    location: '',
    notes: '',
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      
      // TODO: Remplacer par de vrais appels API
      // Simulation de données
      setTimeout(() => {
        setDoctors([
          { id: '1', firstName: 'Marie', lastName: 'Dubois' },
          { id: '2', firstName: 'Pierre', lastName: 'Martin' },
          { id: '3', firstName: 'Sophie', lastName: 'Bernard' },
        ]);

        setShifts([
          {
            id: '1',
            doctorId: '1',
            doctorName: 'Dr. Marie Dubois',
            date: dayjs().format('YYYY-MM-DD'),
            startTime: '08:00',
            endTime: '20:00',
            type: 'DAY',
            location: 'Service des urgences',
            notes: 'Garde normale',
            isConfirmed: true,
          },
          {
            id: '2',
            doctorId: '2',
            doctorName: 'Dr. Pierre Martin',
            date: dayjs().add(1, 'day').format('YYYY-MM-DD'),
            startTime: '20:00',
            endTime: '08:00',
            type: 'NIGHT',
            location: 'Service de nuit',
            notes: '',
            isConfirmed: false,
          },
        ]);
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('Erreur lors du chargement des données:', error);
      setLoading(false);
    }
  };

  const handleOpenDialog = (shift?: Shift) => {
    if (shift) {
      setEditingShift(shift);
      setFormData({
        doctorId: shift.doctorId,
        date: dayjs(shift.date),
        startTime: dayjs(`2000-01-01 ${shift.startTime}`),
        endTime: dayjs(`2000-01-01 ${shift.endTime}`),
        type: shift.type,
        location: shift.location || '',
        notes: shift.notes || '',
      });
    } else {
      setEditingShift(null);
      setFormData({
        doctorId: '',
        date: dayjs(),
        startTime: dayjs().hour(8).minute(0),
        endTime: dayjs().hour(20).minute(0),
        type: 'DAY',
        location: '',
        notes: '',
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingShift(null);
  };

  const handleSave = async () => {
    try {
      const doctor = doctors.find(d => d.id === formData.doctorId);
      if (!doctor) return;

      const shiftData = {
        doctorId: formData.doctorId,
        doctorName: `Dr. ${doctor.firstName} ${doctor.lastName}`,
        date: formData.date.format('YYYY-MM-DD'),
        startTime: formData.startTime.format('HH:mm'),
        endTime: formData.endTime.format('HH:mm'),
        type: formData.type,
        location: formData.location,
        notes: formData.notes,
        isConfirmed: false,
      };

      if (editingShift) {
        // Mise à jour
        setShifts(shifts.map(s => 
          s.id === editingShift.id 
            ? { ...s, ...shiftData }
            : s
        ));
      } else {
        // Création
        const newShift: Shift = {
          id: Date.now().toString(),
          ...shiftData,
        };
        setShifts([...shifts, newShift]);
      }
      
      handleCloseDialog();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
    }
  };

  const handleDelete = async (shiftId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer cette garde ?')) {
      try {
        setShifts(shifts.filter(s => s.id !== shiftId));
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
      }
    }
  };

  const handleToggleConfirmation = async (shiftId: string) => {
    try {
      setShifts(shifts.map(s => 
        s.id === shiftId 
          ? { ...s, isConfirmed: !s.isConfirmed }
          : s
      ));
    } catch (error) {
      console.error('Erreur lors de la confirmation:', error);
    }
  };

  const getTypeLabel = (type: string) => {
    const labels = {
      'DAY': 'Jour',
      'NIGHT': 'Nuit',
      'WEEKEND': 'Week-end',
      'HOLIDAY': 'Jour férié'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const getTypeColor = (type: string) => {
    const colors = {
      'DAY': 'primary',
      'NIGHT': 'secondary',
      'WEEKEND': 'warning',
      'HOLIDAY': 'error'
    };
    return colors[type as keyof typeof colors] || 'default';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Chargement...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Gardes
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Planification et gestion des gardes
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Planifier une garde
        </Button>
      </Box>

      <TableContainer component={Paper} elevation={2}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Médecin</TableCell>
              <TableCell>Date</TableCell>
              <TableCell>Horaires</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Lieu</TableCell>
              <TableCell>Statut</TableCell>
              <TableCell align="right">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {shifts.map((shift) => (
              <TableRow key={shift.id}>
                <TableCell>{shift.doctorName}</TableCell>
                <TableCell>
                  {dayjs(shift.date).format('DD/MM/YYYY')}
                </TableCell>
                <TableCell>
                  {shift.startTime} - {shift.endTime}
                </TableCell>
                <TableCell>
                  <Chip
                    label={getTypeLabel(shift.type)}
                    color={getTypeColor(shift.type) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>{shift.location || '-'}</TableCell>
                <TableCell>
                  <Chip
                    icon={shift.isConfirmed ? <CheckIcon /> : <WarningIcon />}
                    label={shift.isConfirmed ? 'Confirmée' : 'En attente'}
                    color={shift.isConfirmed ? 'success' : 'warning'}
                    size="small"
                    onClick={() => handleToggleConfirmation(shift.id)}
                    clickable
                  />
                </TableCell>
                <TableCell align="right">
                  <IconButton
                    size="small"
                    onClick={() => handleOpenDialog(shift)}
                  >
                    <EditIcon />
                  </IconButton>
                  <IconButton
                    size="small"
                    color="error"
                    onClick={() => handleDelete(shift.id)}
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {shifts.length === 0 && (
        <Alert severity="info" sx={{ mt: 3 }}>
          Aucune garde planifiée. Cliquez sur "Planifier une garde" pour commencer.
        </Alert>
      )}

      {/* Dialog pour ajouter/modifier une garde */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingShift ? 'Modifier la garde' : 'Planifier une garde'}
        </DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} pt={1}>
            <FormControl fullWidth required>
              <InputLabel>Médecin</InputLabel>
              <Select
                value={formData.doctorId}
                onChange={(e) => setFormData({ ...formData, doctorId: e.target.value })}
                label="Médecin"
              >
                {doctors.map((doctor) => (
                  <MenuItem key={doctor.id} value={doctor.id}>
                    Dr. {doctor.firstName} {doctor.lastName}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <DatePicker
              label="Date"
              value={formData.date}
              onChange={(newValue) => newValue && setFormData({ ...formData, date: newValue })}
              slotProps={{ textField: { fullWidth: true, required: true } }}
            />

            <Box display="flex" gap={2}>
              <TimePicker
                label="Heure de début"
                value={formData.startTime}
                onChange={(newValue) => newValue && setFormData({ ...formData, startTime: newValue })}
                slotProps={{ textField: { fullWidth: true, required: true } }}
              />
              <TimePicker
                label="Heure de fin"
                value={formData.endTime}
                onChange={(newValue) => newValue && setFormData({ ...formData, endTime: newValue })}
                slotProps={{ textField: { fullWidth: true, required: true } }}
              />
            </Box>

            <FormControl fullWidth required>
              <InputLabel>Type de garde</InputLabel>
              <Select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                label="Type de garde"
              >
                <MenuItem value="DAY">Jour</MenuItem>
                <MenuItem value="NIGHT">Nuit</MenuItem>
                <MenuItem value="WEEKEND">Week-end</MenuItem>
                <MenuItem value="HOLIDAY">Jour férié</MenuItem>
              </Select>
            </FormControl>

            <TextField
              label="Lieu"
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              fullWidth
            />

            <TextField
              label="Notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              fullWidth
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSave} variant="contained">
            {editingShift ? 'Modifier' : 'Planifier'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Bouton flottant pour mobile */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          display: { xs: 'flex', sm: 'none' },
        }}
        onClick={() => handleOpenDialog()}
      >
        <AddIcon />
      </Fab>
    </Box>
  );
};

export default ShiftsPage;
