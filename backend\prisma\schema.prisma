// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Doctor {
  id         String   @id @default(cuid())
  firstName  String
  lastName   String
  email      String   @unique
  phone      String?
  type       String   // "RESIDENT" ou "ASSISTANT"
  speciality String?
  isActive   Boolean  @default(true)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  shifts Shift[]

  @@map("doctors")
}

model Shift {
  id          String   @id @default(cuid())
  doctorId    String
  date        DateTime
  startTime   String   // Format HH:mm
  endTime     String   // Format HH:mm
  type        String   // "DAY", "NIGHT", "WEEKEND", "HOLIDAY"
  location    String?
  notes       String?
  isConfirmed <PERSON><PERSON><PERSON>  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  doctor Doctor @relation(fields: [doctorId], references: [id], onDelete: Cascade)

  @@map("shifts")
  @@index([doctorId])
  @@index([date])
  @@index([type])
}
