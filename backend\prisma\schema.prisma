// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:./dev.db"
}

model Doctor {
  id        String   @id @default(cuid())
  firstName String
  lastName  String
  type      String   // "RESIDENT" ou "ASSISTANT"
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  assistantShifts Shift[] @relation("AssistantShifts")
  residentShifts  Shift[] @relation("ResidentShifts")

  @@map("doctors")
}

model Shift {
  id           String   @id @default(cuid())
  date         DateTime
  timeSlot     String   // "08:00-20:00" ou "20:00-08:00"
  assistantId  String?
  residentId   String?
  isExcluded   Boolean  @default(false) // Pour les jours fériés
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  assistant Doctor? @relation("AssistantShifts", fields: [assistantId], references: [id])
  resident  Doctor? @relation("ResidentShifts", fields: [residentId], references: [id])

  @@map("shifts")
  @@index([date])
}

model ExcludedDate {
  id          String   @id @default(cuid())
  date        DateTime @unique
  description String   // Ex: "Noël", "Jour de l'An"
  createdAt   DateTime @default(now())

  @@map("excluded_dates")
}
