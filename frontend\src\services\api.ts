import axios from 'axios';

// Configuration de base d'axios
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Types pour les API calls
export interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  type: 'RESIDENT' | 'ASSISTANT';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Shift {
  id: string;
  date: string;
  timeSlot: string;
  assistantId?: string;
  residentId?: string;
  assistant?: Doctor;
  resident?: Doctor;
  isExcluded: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface ExcludedDate {
  id: string;
  date: string;
  description: string;
  createdAt: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Services API
export const doctorService = {
  // Récupérer tous les médecins
  getAll: async (): Promise<Doctor[]> => {
    const response = await api.get<ApiResponse<Doctor[]>>('/doctors');
    return response.data.data || [];
  },

  // Créer un médecin
  create: async (doctor: { firstName: string; lastName: string; type: 'RESIDENT' | 'ASSISTANT' }): Promise<Doctor> => {
    const response = await api.post<ApiResponse<Doctor>>('/doctors', doctor);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Erreur lors de la création');
    }
    return response.data.data!;
  },

  // Mettre à jour un médecin
  update: async (id: string, doctor: Partial<Doctor>): Promise<Doctor> => {
    const response = await api.put<ApiResponse<Doctor>>(`/doctors/${id}`, doctor);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Erreur lors de la mise à jour');
    }
    return response.data.data!;
  },

  // Supprimer un médecin
  delete: async (id: string): Promise<void> => {
    const response = await api.delete<ApiResponse<void>>(`/doctors/${id}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Erreur lors de la suppression');
    }
  },
};

export const shiftService = {
  // Récupérer les gardes d'un mois
  getMonthly: async (year: number, month: number): Promise<{ shifts: Shift[]; statistics: any }> => {
    const response = await api.get<ApiResponse<{ shifts: Shift[]; statistics: any }>>(`/shifts/monthly/${year}/${month}`);
    return response.data.data || { shifts: [], statistics: {} };
  },

  // Créer une garde
  create: async (shift: { date: string; timeSlot: string; assistantId?: string; residentId?: string }): Promise<Shift> => {
    const response = await api.post<ApiResponse<Shift>>('/shifts', shift);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Erreur lors de la création');
    }
    return response.data.data!;
  },

  // Mettre à jour une garde
  update: async (id: string, shift: Partial<Shift>): Promise<Shift> => {
    const response = await api.put<ApiResponse<Shift>>(`/shifts/${id}`, shift);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Erreur lors de la mise à jour');
    }
    return response.data.data!;
  },

  // Supprimer une garde
  delete: async (id: string): Promise<void> => {
    const response = await api.delete<ApiResponse<void>>(`/shifts/${id}`);
    if (!response.data.success) {
      throw new Error(response.data.error || 'Erreur lors de la suppression');
    }
  },
};

export const exportService = {
  // Exporter en Word
  exportToWord: async (data: {
    month: number;
    year: number;
    includeStatistics?: boolean;
    title?: string;
  }): Promise<Blob> => {
    const response = await api.post('/export/word', data, {
      responseType: 'blob',
      headers: {
        'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      },
    });
    return response.data;
  },
};

// Fonction utilitaire pour télécharger un fichier
export const downloadFile = (blob: Blob, filename: string) => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

// Gestion des erreurs globales
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Erreur API:', error);
    
    if (error.response?.status === 404) {
      throw new Error('Ressource non trouvée');
    } else if (error.response?.status === 500) {
      throw new Error('Erreur serveur');
    } else if (error.code === 'NETWORK_ERROR') {
      throw new Error('Erreur de connexion au serveur');
    }
    
    throw error;
  }
);

export default api;
