"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2024 = void 0;
const es2023_1 = require("./es2023");
const es2024_arraybuffer_1 = require("./es2024.arraybuffer");
const es2024_collection_1 = require("./es2024.collection");
const es2024_object_1 = require("./es2024.object");
const es2024_promise_1 = require("./es2024.promise");
const es2024_regexp_1 = require("./es2024.regexp");
const es2024_sharedmemory_1 = require("./es2024.sharedmemory");
const es2024_string_1 = require("./es2024.string");
exports.es2024 = {
    libs: [
        es2023_1.es2023,
        es2024_arraybuffer_1.es2024_arraybuffer,
        es2024_collection_1.es2024_collection,
        es2024_object_1.es2024_object,
        es2024_promise_1.es2024_promise,
        es2024_regexp_1.es2024_regexp,
        es2024_sharedmemory_1.es2024_sharedmemory,
        es2024_string_1.es2024_string,
    ],
    variables: [],
};
