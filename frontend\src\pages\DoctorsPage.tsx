import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Fab,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Person as PersonIcon,
} from '@mui/icons-material';

// Types pour les médecins
interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  type: 'RESIDENT' | 'ASSISTANT';
  isActive: boolean;
  shiftsCount?: number;
  createdAt: string;
  updatedAt: string;
}

const DoctorsPage: React.FC = () => {
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingDoctor, setEditingDoctor] = useState<Doctor | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    type: 'RESIDENT' as 'RESIDENT' | 'ASSISTANT',
  });

  useEffect(() => {
    loadDoctors();
  }, []);

  const loadDoctors = async () => {
    try {
      setLoading(true);
      const { doctorService } = await import('../services/api');
      const doctorsData = await doctorService.getAll();
      setDoctors(doctorsData.map(doctor => ({
        ...doctor,
        shiftsCount: 0 // TODO: Calculer le nombre de gardes
      })));
    } catch (error) {
      console.error('Erreur lors du chargement des médecins:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (doctor?: Doctor) => {
    if (doctor) {
      setEditingDoctor(doctor);
      setFormData({
        firstName: doctor.firstName,
        lastName: doctor.lastName,
        type: doctor.type,
      });
    } else {
      setEditingDoctor(null);
      setFormData({
        firstName: '',
        lastName: '',
        type: 'RESIDENT',
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingDoctor(null);
  };

  const handleSave = async () => {
    try {
      const { doctorService } = await import('../services/api');

      if (editingDoctor) {
        // Mise à jour
        const updatedDoctor = await doctorService.update(editingDoctor.id, {
          firstName: formData.firstName,
          lastName: formData.lastName,
          type: formData.type,
        });
        setDoctors(doctors.map(d =>
          d.id === editingDoctor.id
            ? { ...updatedDoctor, shiftsCount: d.shiftsCount }
            : d
        ));
      } else {
        // Création
        const newDoctor = await doctorService.create({
          firstName: formData.firstName,
          lastName: formData.lastName,
          type: formData.type,
        });
        setDoctors([...doctors, { ...newDoctor, shiftsCount: 0 }]);
      }

      handleCloseDialog();
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      // TODO: Afficher une notification d'erreur
    }
  };

  const handleDelete = async (doctorId: string) => {
    if (window.confirm('Êtes-vous sûr de vouloir supprimer ce médecin ?')) {
      try {
        const { doctorService } = await import('../services/api');
        await doctorService.delete(doctorId);
        setDoctors(doctors.filter(d => d.id !== doctorId));
      } catch (error) {
        console.error('Erreur lors de la suppression:', error);
        // TODO: Afficher une notification d'erreur
      }
    }
  };

  const getTypeLabel = (type: string) => {
    return type === 'RESIDENT' ? 'Résident' : 'Assistant';
  };

  const getTypeColor = (type: string) => {
    return type === 'RESIDENT' ? 'primary' : 'secondary';
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Chargement...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Médecins
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Gestion des médecins résidents et assistants
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Ajouter un médecin
        </Button>
      </Box>

      <Grid container spacing={3}>
        {doctors.map((doctor) => (
          <Grid item xs={12} sm={6} md={4} key={doctor.id}>
            <Card elevation={2}>
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <PersonIcon color="primary" />
                    <Typography variant="h6">
                      {doctor.firstName} {doctor.lastName}
                    </Typography>
                  </Box>
                  <Box>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(doctor)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDelete(doctor.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </Box>

                <Box mb={2}>
                  <Chip
                    label={getTypeLabel(doctor.type)}
                    color={getTypeColor(doctor.type) as any}
                    size="small"
                  />
                  {!doctor.isActive && (
                    <Chip
                      label="Inactif"
                      color="error"
                      size="small"
                      sx={{ ml: 1 }}
                    />
                  )}
                </Box>

                {doctor.speciality && (
                  <Typography variant="body2" color="textSecondary" gutterBottom>
                    {doctor.speciality}
                  </Typography>
                )}

                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <EmailIcon fontSize="small" color="action" />
                  <Typography variant="body2">{doctor.email}</Typography>
                </Box>

                {doctor.phone && (
                  <Box display="flex" alignItems="center" gap={1} mb={1}>
                    <PhoneIcon fontSize="small" color="action" />
                    <Typography variant="body2">{doctor.phone}</Typography>
                  </Box>
                )}

                <Typography variant="body2" color="textSecondary">
                  {doctor.shiftsCount || 0} garde(s) assignée(s)
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {doctors.length === 0 && (
        <Alert severity="info" sx={{ mt: 3 }}>
          Aucun médecin enregistré. Cliquez sur "Ajouter un médecin" pour commencer.
        </Alert>
      )}

      {/* Dialog pour ajouter/modifier un médecin */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingDoctor ? 'Modifier le médecin' : 'Ajouter un médecin'}
        </DialogTitle>
        <DialogContent>
          <Box display="flex" flexDirection="column" gap={2} pt={1}>
            <TextField
              label="Prénom"
              value={formData.firstName}
              onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Nom"
              value={formData.lastName}
              onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Email"
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              fullWidth
              required
            />
            <TextField
              label="Téléphone"
              value={formData.phone}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              fullWidth
            />
            <FormControl fullWidth required>
              <InputLabel>Type</InputLabel>
              <Select
                value={formData.type}
                onChange={(e) => setFormData({ ...formData, type: e.target.value as any })}
                label="Type"
              >
                <MenuItem value="RESIDENT">Résident</MenuItem>
                <MenuItem value="ASSISTANT">Assistant</MenuItem>
              </Select>
            </FormControl>
            <TextField
              label="Spécialité"
              value={formData.speciality}
              onChange={(e) => setFormData({ ...formData, speciality: e.target.value })}
              fullWidth
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button onClick={handleSave} variant="contained">
            {editingDoctor ? 'Modifier' : 'Ajouter'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Bouton flottant pour mobile */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          display: { xs: 'flex', sm: 'none' },
        }}
        onClick={() => handleOpenDialog()}
      >
        <AddIcon />
      </Fab>
    </Box>
  );
};

export default DoctorsPage;
