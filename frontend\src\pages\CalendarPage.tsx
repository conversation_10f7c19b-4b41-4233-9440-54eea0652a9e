import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Paper,
  Tooltip,
} from '@mui/material';
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  FileDownload as ExportIcon,
  Today as TodayIcon,
} from '@mui/icons-material';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/fr';

dayjs.locale('fr');

// Types pour le calendrier
interface CalendarShift {
  id: string;
  doctorName: string;
  startTime: string;
  endTime: string;
  type: 'DAY' | 'NIGHT' | 'WEEKEND' | 'HOLIDAY';
  isConfirmed: boolean;
}

interface CalendarDay {
  date: Dayjs;
  shifts: CalendarShift[];
  isCurrentMonth: boolean;
  isToday: boolean;
}

const CalendarPage: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(dayjs());
  const [calendarDays, setCalendarDays] = useState<CalendarDay[]>([]);
  const [selectedMonth, setSelectedMonth] = useState(dayjs().month());
  const [selectedYear, setSelectedYear] = useState(dayjs().year());
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCalendarData();
  }, [selectedMonth, selectedYear]);

  const loadCalendarData = async () => {
    try {
      setLoading(true);
      
      // Créer le calendrier pour le mois sélectionné
      const startOfMonth = dayjs().year(selectedYear).month(selectedMonth).startOf('month');
      const endOfMonth = startOfMonth.endOf('month');
      const startOfCalendar = startOfMonth.startOf('week');
      const endOfCalendar = endOfMonth.endOf('week');

      const days: CalendarDay[] = [];
      let currentDay = startOfCalendar;

      // TODO: Remplacer par un vrai appel API
      // Simulation de données de gardes
      const mockShifts: Record<string, CalendarShift[]> = {
        [dayjs().format('YYYY-MM-DD')]: [
          {
            id: '1',
            doctorName: 'Dr. Dubois',
            startTime: '08:00',
            endTime: '20:00',
            type: 'DAY',
            isConfirmed: true,
          },
        ],
        [dayjs().add(1, 'day').format('YYYY-MM-DD')]: [
          {
            id: '2',
            doctorName: 'Dr. Martin',
            startTime: '20:00',
            endTime: '08:00',
            type: 'NIGHT',
            isConfirmed: false,
          },
        ],
        [dayjs().add(2, 'day').format('YYYY-MM-DD')]: [
          {
            id: '3',
            doctorName: 'Dr. Bernard',
            startTime: '08:00',
            endTime: '20:00',
            type: 'WEEKEND',
            isConfirmed: true,
          },
          {
            id: '4',
            doctorName: 'Dr. Petit',
            startTime: '20:00',
            endTime: '08:00',
            type: 'NIGHT',
            isConfirmed: true,
          },
        ],
      };

      while (currentDay.isBefore(endOfCalendar) || currentDay.isSame(endOfCalendar, 'day')) {
        const dateKey = currentDay.format('YYYY-MM-DD');
        days.push({
          date: currentDay,
          shifts: mockShifts[dateKey] || [],
          isCurrentMonth: currentDay.month() === selectedMonth,
          isToday: currentDay.isSame(dayjs(), 'day'),
        });
        currentDay = currentDay.add(1, 'day');
      }

      setCalendarDays(days);
      setLoading(false);
    } catch (error) {
      console.error('Erreur lors du chargement du calendrier:', error);
      setLoading(false);
    }
  };

  const handlePreviousMonth = () => {
    if (selectedMonth === 0) {
      setSelectedMonth(11);
      setSelectedYear(selectedYear - 1);
    } else {
      setSelectedMonth(selectedMonth - 1);
    }
  };

  const handleNextMonth = () => {
    if (selectedMonth === 11) {
      setSelectedMonth(0);
      setSelectedYear(selectedYear + 1);
    } else {
      setSelectedMonth(selectedMonth + 1);
    }
  };

  const handleToday = () => {
    const today = dayjs();
    setSelectedMonth(today.month());
    setSelectedYear(today.year());
  };

  const handleExport = async () => {
    try {
      // TODO: Implémenter l'export Word
      console.log('Export du planning mensuel');
      alert('Fonctionnalité d\'export à implémenter');
    } catch (error) {
      console.error('Erreur lors de l\'export:', error);
    }
  };

  const getTypeColor = (type: string) => {
    const colors = {
      'DAY': '#2E86AB',
      'NIGHT': '#A23B72',
      'WEEKEND': '#F18F01',
      'HOLIDAY': '#C73E1D'
    };
    return colors[type as keyof typeof colors] || '#666';
  };

  const getTypeLabel = (type: string) => {
    const labels = {
      'DAY': 'J',
      'NIGHT': 'N',
      'WEEKEND': 'WE',
      'HOLIDAY': 'JF'
    };
    return labels[type as keyof typeof labels] || type;
  };

  const weekDays = ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'];
  const months = [
    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <Typography>Chargement...</Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* En-tête */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Calendrier des gardes
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Vue mensuelle du planning
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<TodayIcon />}
            onClick={handleToday}
          >
            Aujourd'hui
          </Button>
          <Button
            variant="contained"
            startIcon={<ExportIcon />}
            onClick={handleExport}
          >
            Exporter
          </Button>
        </Box>
      </Box>

      {/* Contrôles de navigation */}
      <Card elevation={2} sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" justifyContent="space-between" alignItems="center">
            <IconButton onClick={handlePreviousMonth}>
              <ChevronLeftIcon />
            </IconButton>
            
            <Box display="flex" gap={2} alignItems="center">
              <FormControl size="small">
                <Select
                  value={selectedMonth}
                  onChange={(e) => setSelectedMonth(e.target.value as number)}
                >
                  {months.map((month, index) => (
                    <MenuItem key={index} value={index}>
                      {month}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              <FormControl size="small">
                <Select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(e.target.value as number)}
                >
                  {Array.from({ length: 10 }, (_, i) => dayjs().year() - 5 + i).map((year) => (
                    <MenuItem key={year} value={year}>
                      {year}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>

            <IconButton onClick={handleNextMonth}>
              <ChevronRightIcon />
            </IconButton>
          </Box>
        </CardContent>
      </Card>

      {/* Calendrier */}
      <Paper elevation={2}>
        <Box p={2}>
          {/* En-têtes des jours */}
          <Grid container spacing={1} sx={{ mb: 1 }}>
            {weekDays.map((day) => (
              <Grid item xs key={day}>
                <Box
                  display="flex"
                  justifyContent="center"
                  alignItems="center"
                  py={1}
                  sx={{
                    backgroundColor: 'primary.main',
                    color: 'white',
                    borderRadius: 1,
                    fontWeight: 'bold',
                  }}
                >
                  {day}
                </Box>
              </Grid>
            ))}
          </Grid>

          {/* Jours du calendrier */}
          <Grid container spacing={1}>
            {calendarDays.map((day, index) => (
              <Grid item xs key={index}>
                <Box
                  sx={{
                    minHeight: 120,
                    border: 1,
                    borderColor: 'divider',
                    borderRadius: 1,
                    p: 1,
                    backgroundColor: day.isCurrentMonth ? 'background.paper' : 'action.hover',
                    position: 'relative',
                  }}
                >
                  {/* Numéro du jour */}
                  <Typography
                    variant="body2"
                    sx={{
                      fontWeight: day.isToday ? 'bold' : 'normal',
                      color: day.isToday ? 'primary.main' : day.isCurrentMonth ? 'text.primary' : 'text.secondary',
                    }}
                  >
                    {day.date.date()}
                  </Typography>

                  {/* Gardes du jour */}
                  <Box mt={1}>
                    {day.shifts.map((shift) => (
                      <Tooltip
                        key={shift.id}
                        title={`${shift.doctorName} - ${shift.startTime}-${shift.endTime} ${shift.isConfirmed ? '(Confirmée)' : '(En attente)'}`}
                      >
                        <Chip
                          label={getTypeLabel(shift.type)}
                          size="small"
                          sx={{
                            backgroundColor: getTypeColor(shift.type),
                            color: 'white',
                            fontSize: '0.7rem',
                            height: 20,
                            mb: 0.5,
                            mr: 0.5,
                            opacity: shift.isConfirmed ? 1 : 0.7,
                            border: shift.isConfirmed ? 'none' : '1px dashed white',
                          }}
                        />
                      </Tooltip>
                    ))}
                  </Box>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Paper>

      {/* Légende */}
      <Card elevation={1} sx={{ mt: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Légende
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={2}>
            <Box display="flex" alignItems="center" gap={1}>
              <Box
                sx={{
                  width: 16,
                  height: 16,
                  backgroundColor: getTypeColor('DAY'),
                  borderRadius: 0.5,
                }}
              />
              <Typography variant="body2">Jour (J)</Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <Box
                sx={{
                  width: 16,
                  height: 16,
                  backgroundColor: getTypeColor('NIGHT'),
                  borderRadius: 0.5,
                }}
              />
              <Typography variant="body2">Nuit (N)</Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <Box
                sx={{
                  width: 16,
                  height: 16,
                  backgroundColor: getTypeColor('WEEKEND'),
                  borderRadius: 0.5,
                }}
              />
              <Typography variant="body2">Week-end (WE)</Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <Box
                sx={{
                  width: 16,
                  height: 16,
                  backgroundColor: getTypeColor('HOLIDAY'),
                  borderRadius: 0.5,
                }}
              />
              <Typography variant="body2">Jour férié (JF)</Typography>
            </Box>
            <Box display="flex" alignItems="center" gap={1}>
              <Box
                sx={{
                  width: 16,
                  height: 16,
                  backgroundColor: '#666',
                  borderRadius: 0.5,
                  border: '1px dashed white',
                }}
              />
              <Typography variant="body2">Non confirmée</Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
};

export default CalendarPage;
