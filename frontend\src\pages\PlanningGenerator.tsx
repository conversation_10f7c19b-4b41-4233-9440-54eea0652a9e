import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Grid,
  Card,
  CardContent,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Alert,
  Fab
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Download as DownloadIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';

// Types
interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  type: 'RESIDENT' | 'ASSISTANT';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface SelectedDoctor {
  doctorId: string;
  doctor: Doctor;
  count: number;
}

interface PlanningSlot {
  id: string;
  date: string;
  timeSlot: string;
  assistantId?: string;
  residentId?: string;
  assistant?: Doctor;
  resident?: Doctor;
}

const timeSlots = [
  '08h00-16h00',
  '16h00-00h00',
  '00h00-08h00'
];

const PlanningGenerator: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // Récupération des données depuis le Dashboard
  const { selectedDoctors, selectedMonth, selectedYear } = location.state || {};
  
  const [planning, setPlanning] = useState<PlanningSlot[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingSlot, setEditingSlot] = useState<PlanningSlot | null>(null);
  const [selectedAssistant, setSelectedAssistant] = useState('');
  const [selectedResident, setSelectedResident] = useState('');

  const months = [
    'Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
    'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre'
  ];

  useEffect(() => {
    if (!selectedDoctors || !selectedMonth || selectedYear === undefined) {
      navigate('/');
      return;
    }
    
    loadDoctors();
    generateInitialPlanning();
  }, [selectedDoctors, selectedMonth, selectedYear]);

  const loadDoctors = async () => {
    try {
      const { doctorService } = await import('../services/api');
      const doctorsData = await doctorService.getAll();
      setDoctors(doctorsData);
    } catch (error) {
      console.error('Erreur lors du chargement des médecins:', error);
    }
  };

  const generateInitialPlanning = () => {
    const year = selectedYear;
    const month = selectedMonth;
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    
    const slots: PlanningSlot[] = [];
    
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dateString = date.toISOString().split('T')[0];
      
      timeSlots.forEach((timeSlot, index) => {
        slots.push({
          id: `${dateString}-${index}`,
          date: dateString,
          timeSlot,
        });
      });
    }
    
    setPlanning(slots);
  };

  const handleEditSlot = (slot: PlanningSlot) => {
    setEditingSlot(slot);
    setSelectedAssistant(slot.assistantId || '');
    setSelectedResident(slot.residentId || '');
    setDialogOpen(true);
  };

  const handleSaveSlot = () => {
    if (!editingSlot) return;

    const updatedPlanning = planning.map(slot => {
      if (slot.id === editingSlot.id) {
        const assistant = selectedAssistant ? doctors.find(d => d.id === selectedAssistant) : undefined;
        const resident = selectedResident ? doctors.find(d => d.id === selectedResident) : undefined;
        
        return {
          ...slot,
          assistantId: selectedAssistant || undefined,
          residentId: selectedResident || undefined,
          assistant,
          resident
        };
      }
      return slot;
    });

    setPlanning(updatedPlanning);
    setDialogOpen(false);
    setEditingSlot(null);
    setSelectedAssistant('');
    setSelectedResident('');
  };

  const handleClearSlot = (slotId: string) => {
    const updatedPlanning = planning.map(slot => {
      if (slot.id === slotId) {
        return {
          ...slot,
          assistantId: undefined,
          residentId: undefined,
          assistant: undefined,
          resident: undefined
        };
      }
      return slot;
    });

    setPlanning(updatedPlanning);
  };

  const handleExportToWord = async () => {
    try {
      setLoading(true);
      
      // Créer les gardes dans la base de données
      const { shiftService } = await import('../services/api');
      
      for (const slot of planning) {
        if (slot.assistantId || slot.residentId) {
          await shiftService.create({
            date: slot.date,
            timeSlot: slot.timeSlot,
            assistantId: slot.assistantId,
            residentId: slot.residentId
          });
        }
      }

      // Exporter en Word
      const { exportService, downloadFile } = await import('../services/api');
      
      const blob = await exportService.exportToWord({
        month: selectedMonth + 1,
        year: selectedYear,
        includeStatistics: true,
        title: `Planning des Gardes - ${months[selectedMonth]} ${selectedYear}`
      });

      const filename = `planning-gardes-${selectedYear}-${(selectedMonth + 1).toString().padStart(2, '0')}.docx`;
      downloadFile(blob, filename);
      
      alert('Planning exporté avec succès !');
    } catch (error) {
      console.error('Erreur lors de l\'export:', error);
      alert('Erreur lors de l\'export du planning');
    } finally {
      setLoading(false);
    }
  };

  const assistants = doctors.filter(d => d.type === 'ASSISTANT' && d.isActive);
  const residents = doctors.filter(d => d.type === 'RESIDENT' && d.isActive);

  const groupedPlanning = planning.reduce((acc, slot) => {
    if (!acc[slot.date]) {
      acc[slot.date] = [];
    }
    acc[slot.date].push(slot);
    return acc;
  }, {} as Record<string, PlanningSlot[]>);

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
        <IconButton onClick={() => navigate('/')} color="primary">
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>
          Générateur de Planning
        </Typography>
        <Chip 
          label={`${months[selectedMonth]} ${selectedYear}`}
          color="primary"
          sx={{ ml: 'auto' }}
        />
      </Box>

      {/* Informations sélectionnées */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Médecins sélectionnés
          </Typography>
          <Grid container spacing={2}>
            {selectedDoctors?.map((selection: SelectedDoctor) => (
              <Grid item key={selection.doctorId}>
                <Chip
                  label={`${selection.doctor.firstName} ${selection.doctor.lastName} (${selection.count})`}
                  color={selection.doctor.type === 'ASSISTANT' ? 'primary' : 'secondary'}
                  variant="outlined"
                />
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Planning Table */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Planning du mois
            </Typography>
            <Button
              variant="contained"
              startIcon={<DownloadIcon />}
              onClick={handleExportToWord}
              disabled={loading}
              sx={{ 
                background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
                '&:hover': {
                  background: 'linear-gradient(45deg, #1565c0, #1976d2)',
                }
              }}
            >
              Exporter en Word
            </Button>
          </Box>

          <TableContainer component={Paper} sx={{ maxHeight: 600 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Horaire</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Assistant</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Résident</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {Object.entries(groupedPlanning).map(([date, slots]) => (
                  slots.map((slot, index) => (
                    <TableRow key={slot.id} hover>
                      <TableCell>
                        {index === 0 && new Date(date).toLocaleDateString('fr-FR')}
                      </TableCell>
                      <TableCell>{slot.timeSlot}</TableCell>
                      <TableCell>
                        {slot.assistant ? (
                          <Chip
                            label={`${slot.assistant.firstName} ${slot.assistant.lastName}`}
                            color="primary"
                            size="small"
                          />
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Non assigné
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        {slot.resident ? (
                          <Chip
                            label={`${slot.resident.firstName} ${slot.resident.lastName}`}
                            color="secondary"
                            size="small"
                          />
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Non assigné
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleEditSlot(slot)}
                          color="primary"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleClearSlot(slot.id)}
                          color="error"
                          disabled={!slot.assistantId && !slot.residentId}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* Dialog d'édition */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Modifier la garde - {editingSlot?.date} {editingSlot?.timeSlot}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Assistant</InputLabel>
                <Select
                  value={selectedAssistant}
                  onChange={(e) => setSelectedAssistant(e.target.value)}
                  label="Assistant"
                >
                  <MenuItem value="">
                    <em>Aucun</em>
                  </MenuItem>
                  {assistants.map((doctor) => (
                    <MenuItem key={doctor.id} value={doctor.id}>
                      {doctor.firstName} {doctor.lastName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>Résident</InputLabel>
                <Select
                  value={selectedResident}
                  onChange={(e) => setSelectedResident(e.target.value)}
                  label="Résident"
                >
                  <MenuItem value="">
                    <em>Aucun</em>
                  </MenuItem>
                  {residents.map((doctor) => (
                    <MenuItem key={doctor.id} value={doctor.id}>
                      {doctor.firstName} {doctor.lastName}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Annuler
          </Button>
          <Button onClick={handleSaveSlot} variant="contained">
            Sauvegarder
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default PlanningGenerator;
