{"name": "gestion<PERSON>e", "version": "1.0.0", "description": "Application de gestion des gardes hospitalières", "main": "electron/main.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:full": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"wait-on http://localhost:5173 http://localhost:3001 && npm run electron:dev\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "electron:dev": "electron .", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "electron:build": "electron-builder", "prisma:generate": "cd backend && npx prisma generate", "prisma:migrate": "cd backend && npx prisma migrate dev", "prisma:studio": "cd backend && npx prisma studio"}, "keywords": ["electron", "react", "nodejs", "prisma", "hospital", "garde"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^9.1.2", "electron": "^36.3.2", "electron-builder": "^26.0.12", "wait-on": "^8.0.3"}}