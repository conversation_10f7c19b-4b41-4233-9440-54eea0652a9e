{"name": "gestiongarde-backend", "version": "1.0.0", "description": "Backend API pour la gestion des gardes hospitalières", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "prisma:seed": "tsx src/seed.ts", "test": "jest", "test:watch": "jest --watch"}, "keywords": ["nodejs", "express", "prisma", "typescript", "hospital", "garde"], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^5.7.1", "cors": "^2.8.5", "date-fns": "^3.0.6", "docx": "^8.5.0", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "morgan": "^1.10.0", "officegen": "^0.6.5", "zod": "^3.22.4"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/morgan": "^1.9.9", "@types/node": "^20.10.5", "jest": "^29.7.0", "nodemon": "^3.0.2", "prisma": "^5.7.1", "tsx": "^4.6.2", "typescript": "^5.3.3"}}