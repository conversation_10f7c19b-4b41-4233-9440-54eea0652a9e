import express from 'express';
import { z } from 'zod';
import { prisma } from '../index';

const router = express.Router();

// Schémas de validation
const createExcludedDateSchema = z.object({
  date: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: 'Date invalide'
  }),
  description: z.string().min(1, 'La description est requise')
});

const updateExcludedDateSchema = createExcludedDateSchema.partial();

// GET /api/excluded-dates - Récupérer toutes les dates exclues
router.get('/', async (req, res) => {
  try {
    const { year, month } = req.query;
    
    const where: any = {};
    
    if (year) {
      const startOfYear = new Date(parseInt(year as string), 0, 1);
      const endOfYear = new Date(parseInt(year as string), 11, 31);
      where.date = {
        gte: startOfYear,
        lte: endOfYear
      };
    }
    
    if (month && year) {
      const startOfMonth = new Date(parseInt(year as string), parseInt(month as string) - 1, 1);
      const endOfMonth = new Date(parseInt(year as string), parseInt(month as string), 0);
      where.date = {
        gte: startOfMonth,
        lte: endOfMonth
      };
    }

    const excludedDates = await prisma.excludedDate.findMany({
      where,
      orderBy: { date: 'asc' }
    });

    res.json({
      success: true,
      data: excludedDates
    });
  } catch (error) {
    console.error('Erreur lors de la récupération des dates exclues:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la récupération des dates exclues'
    });
  }
});

// GET /api/excluded-dates/:id - Récupérer une date exclue par ID
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const excludedDate = await prisma.excludedDate.findUnique({
      where: { id }
    });

    if (!excludedDate) {
      return res.status(404).json({
        success: false,
        error: 'Date exclue non trouvée'
      });
    }

    res.json({
      success: true,
      data: excludedDate
    });
  } catch (error) {
    console.error('Erreur lors de la récupération de la date exclue:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la récupération de la date exclue'
    });
  }
});

// POST /api/excluded-dates - Créer une nouvelle date exclue
router.post('/', async (req, res) => {
  try {
    const validatedData = createExcludedDateSchema.parse(req.body);

    // Vérifier si la date existe déjà
    const existingDate = await prisma.excludedDate.findFirst({
      where: { 
        date: new Date(validatedData.date)
      }
    });

    if (existingDate) {
      return res.status(400).json({
        success: false,
        error: 'Cette date est déjà exclue'
      });
    }

    const excludedDate = await prisma.excludedDate.create({
      data: {
        date: new Date(validatedData.date),
        description: validatedData.description
      }
    });

    res.status(201).json({
      success: true,
      data: excludedDate,
      message: 'Date exclue créée avec succès'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Données invalides',
        details: error.errors
      });
    }

    console.error('Erreur lors de la création de la date exclue:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la création de la date exclue'
    });
  }
});

// PUT /api/excluded-dates/:id - Mettre à jour une date exclue
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const validatedData = updateExcludedDateSchema.parse(req.body);

    // Vérifier si la date exclue existe
    const existingExcludedDate = await prisma.excludedDate.findUnique({
      where: { id }
    });

    if (!existingExcludedDate) {
      return res.status(404).json({
        success: false,
        error: 'Date exclue non trouvée'
      });
    }

    const updateData: any = {};
    if (validatedData.date) {
      updateData.date = new Date(validatedData.date);
    }
    if (validatedData.description) {
      updateData.description = validatedData.description;
    }

    const excludedDate = await prisma.excludedDate.update({
      where: { id },
      data: updateData
    });

    res.json({
      success: true,
      data: excludedDate,
      message: 'Date exclue mise à jour avec succès'
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        success: false,
        error: 'Données invalides',
        details: error.errors
      });
    }

    console.error('Erreur lors de la mise à jour de la date exclue:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la mise à jour de la date exclue'
    });
  }
});

// DELETE /api/excluded-dates/:id - Supprimer une date exclue
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Vérifier si la date exclue existe
    const existingExcludedDate = await prisma.excludedDate.findUnique({
      where: { id }
    });

    if (!existingExcludedDate) {
      return res.status(404).json({
        success: false,
        error: 'Date exclue non trouvée'
      });
    }

    await prisma.excludedDate.delete({
      where: { id }
    });

    res.json({
      success: true,
      message: 'Date exclue supprimée avec succès'
    });
  } catch (error) {
    console.error('Erreur lors de la suppression de la date exclue:', error);
    res.status(500).json({
      success: false,
      error: 'Erreur lors de la suppression de la date exclue'
    });
  }
});

export default router;
