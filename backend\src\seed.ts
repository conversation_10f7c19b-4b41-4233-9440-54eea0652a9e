import { PrismaClient } from '@prisma/client';
import { DoctorType } from '@shared/types';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Début du seeding...');

  // Nettoyer les données existantes
  await prisma.shift.deleteMany();
  await prisma.excludedDate.deleteMany();
  await prisma.doctor.deleteMany();

  // Créer des médecins de test
  const doctors = await Promise.all([
    prisma.doctor.create({
      data: {
        firstName: 'Marie',
        lastName: 'Dubois',
        type: DoctorType.RESIDENT
      }
    }),
    prisma.doctor.create({
      data: {
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        type: DoctorType.ASSISTANT
      }
    }),
    prisma.doctor.create({
      data: {
        firstName: '<PERSON>',
        lastName: '<PERSON>',
        type: DoctorType.RESIDENT
      }
    }),
    prisma.doctor.create({
      data: {
        firstName: '<PERSON>',
        lastName: 'Petit',
        type: DoctorType.ASSISTANT
      }
    }),
    prisma.doctor.create({
      data: {
        firstName: '<PERSON>',
        lastName: 'Moreau',
        type: DoctorType.RESIDENT
      }
    }),
    prisma.doctor.create({
      data: {
        firstName: '<PERSON>',
        lastName: 'Leroy',
        type: DoctorType.ASSISTANT
      }
    })
  ]);

  console.log(`✅ ${doctors.length} médecins créés`);

  // Créer quelques dates exclues (jours fériés)
  const currentYear = new Date().getFullYear();
  const excludedDates = await Promise.all([
    prisma.excludedDate.create({
      data: {
        date: new Date(currentYear, 0, 1), // Nouvel An
        description: 'Nouvel An'
      }
    }),
    prisma.excludedDate.create({
      data: {
        date: new Date(currentYear, 4, 1), // Fête du travail
        description: 'Fête du travail'
      }
    }),
    prisma.excludedDate.create({
      data: {
        date: new Date(currentYear, 6, 14), // Fête nationale
        description: 'Fête nationale'
      }
    }),
    prisma.excludedDate.create({
      data: {
        date: new Date(currentYear, 11, 25), // Noël
        description: 'Noël'
      }
    })
  ]);

  console.log(`✅ ${excludedDates.length} dates exclues créées`);

  // Créer quelques gardes d'exemple pour le mois courant
  const currentDate = new Date();
  const currentMonth = currentDate.getMonth();
  const assistants = doctors.filter(d => d.type === DoctorType.ASSISTANT);
  const residents = doctors.filter(d => d.type === DoctorType.RESIDENT);

  const shifts = [];

  // Créer 34 créneaux pour le mois (exemple)
  for (let i = 1; i <= 10; i++) {
    const date = new Date(currentYear, currentMonth, i);

    // Garde de jour
    shifts.push({
      date: date,
      timeSlot: '08:00-20:00',
      assistantId: assistants[i % assistants.length]?.id,
      residentId: residents[i % residents.length]?.id,
      isExcluded: false
    });

    // Garde de nuit
    if (i <= 5) {
      shifts.push({
        date: date,
        timeSlot: '20:00-08:00',
        assistantId: assistants[(i + 1) % assistants.length]?.id,
        residentId: residents[(i + 1) % residents.length]?.id,
        isExcluded: false
      });
    }
  }

  const createdShifts = await Promise.all(
    shifts.map(shift =>
      prisma.shift.create({
        data: shift
      })
    )
  );

  console.log(`✅ ${createdShifts.length} gardes créées`);

  // Afficher un résumé
  const totalDoctors = await prisma.doctor.count();
  const totalShifts = await prisma.shift.count();
  const totalExcludedDates = await prisma.excludedDate.count();

  console.log('\n📊 Résumé du seeding:');
  console.log(`   • Médecins: ${totalDoctors}`);
  console.log(`   • Gardes: ${totalShifts}`);
  console.log(`   • Dates exclues: ${totalExcludedDates}`);

  console.log('\n🎉 Seeding terminé avec succès!');
}

main()
  .catch((e) => {
    console.error('❌ Erreur lors du seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
