// Types partagés entre frontend et backend

export interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  type: DoctorType;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum DoctorType {
  RESIDENT = 'RESIDENT',
  ASSISTANT = 'ASSISTANT'
}

export interface Shift {
  id: string;
  date: Date;
  timeSlot: string; // "08:00-20:00" ou "20:00-08:00"
  assistantId?: string;
  residentId?: string;
  assistant?: Doctor;
  resident?: Doctor;
  isExcluded: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ExcludedDate {
  id: string;
  date: Date;
  description: string;
  createdAt: Date;
}

export interface MonthlySchedule {
  month: number;
  year: number;
  shifts: Shift[];
  statistics: {
    totalShifts: number;
    shiftsByDoctor: Record<string, number>;
  };
}

// DTOs pour les API
export interface CreateDoctorDto {
  firstName: string;
  lastName: string;
  type: DoctorType;
}

export interface UpdateDoctorDto {
  firstName?: string;
  lastName?: string;
  type?: DoctorType;
  isActive?: boolean;
}

export interface CreateShiftDto {
  date: Date;
  timeSlot: string;
  assistantId?: string;
  residentId?: string;
}

export interface UpdateShiftDto {
  date?: Date;
  timeSlot?: string;
  assistantId?: string;
  residentId?: string;
  isExcluded?: boolean;
}

export interface CreateExcludedDateDto {
  date: Date;
  description: string;
}

// Types pour l'export Word
export interface ExportData {
  month: number;
  year: number;
  shifts: Shift[];
  doctors: Doctor[];
  title: string;
}

export interface ExportOptions {
  includeStatistics: boolean;
  includeNotes: boolean;
  groupByDoctor: boolean;
  format: 'table' | 'calendar';
}

// Types pour les réponses API
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Types pour les filtres
export interface ShiftFilters {
  doctorId?: string;
  startDate?: Date;
  endDate?: Date;
  type?: ShiftType;
  isConfirmed?: boolean;
}

export interface DoctorFilters {
  type?: DoctorType;
  isActive?: boolean;
  speciality?: string;
  search?: string; // Recherche par nom ou email
}
